package com.huatek.frame.modules.business.rest.aps;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.gexin.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.config.MinioProperties;
import com.huatek.frame.modules.business.conf.ApsConfiguration;
import com.huatek.frame.modules.business.service.ProductionTaskService;
import com.huatek.frame.modules.business.service.dto.ProductionTaskAPSDTO;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 下发计划Controller层
 */
@Api(tags = "下发计划管理")
@RestController
@RequestMapping("/api/schedulePlan")
@Slf4j
public class ApsSchedulePlanController {

    @Autowired
    private ProductionTaskService productionTaskService;

    /**
     * 恢复重排
     *
     * @param dto     恢复重排请求DTO  List<String> ids
     * @param request
     * @return
     */
    @Log("恢复重排")
    @ApiOperation(value = "恢复重排")
    @TorchPerm("issuePlan:recoverReschedule")
    public Object recoverAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("PUT");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/Recover");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 暂停重排
     *
     * @param dto     暂停重排请求DTO List<String> ids
     * @param request
     * @return
     */
    @Log("暂停重排")
    @ApiOperation(value = "暂停重排")
    @TorchPerm("issuePlan:pauseReschedule")

    @PutMapping(value = "/Stop", produces = {"application/json;charset=utf-8"})
    public Object stopAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("PUT");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/Stop");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 完成
     *
     * @param dto     完成计划请求DTO List<String> ids
     * @param request
     * @return
     */
    @Log("完成计划")
    @ApiOperation(value = "完成计划")
    @TorchPerm("issuePlan:complete")

    public Object finishAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("PUT");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/Finish");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 滚动排产
     *
     * @param dto     滚动排产请求DTO ApsSchedulePlanRuleDto
     * @param request
     * @return
     */
    @Log("滚动重排")
    @ApiOperation(value = "滚动重排")
    @TorchPerm("issuePlan:scrollReschedule")
    @PostMapping(value = "/rolling", produces = {"application/json;charset=utf-8"})
    public Object RollingScheduleAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/RollingSchedule");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 取消重排
     *
     * @param dto     取消重排请求DTO List<String> ids
     * @param request
     * @return
     */
    @Log("取消重排")
    @ApiOperation(value = "取消重排")

    @TorchPerm("issuePlan:cancelReschedule")
    @PutMapping(value = "/cancel", produces = {"application/json;charset=utf-8"})
    public Object CancelAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("PUT");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/Cancel");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 预警查询
     *
     * @param dto     预警查询
     * @param request PlanDeviationWarningPagedDto
     *                PlanDeviationType 查所有:-1  开工延迟预警:0  完工延迟预警:1  未开工预警:2  未完工预警:3
     * @return
     */
    @Log("计划vs实际偏差预警查询")
    @ApiOperation("计划vs实际偏差预警查询")
    @PostMapping(value = "/planDeviationWarning", produces = {"application/json;charset=utf-8"})
    public Object GetPlanDeviationWarningAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/PlanDeviationWarning");
        return HttpClientUtil.callMes(request, inputParamDto);
    }


    @Log("分页查询下发计划")
    @ApiOperation("分页查询下发计划")
    @PostMapping(value = "/SchedulePlanList")
    public Object GetPagedAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/SchedulePlanList");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 设备甘特图
     */
    @Log("设备甘特图")
    @ApiOperation("设备甘特图")
    @PostMapping(value = "/deviceGantt")
    public Object GetDeviceGanttPLanAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/deviceGantt");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 计划甘特图
     */
    @Log("计划甘特图")
    @ApiOperation("计划甘特图")
    @PostMapping(value = "/planGantt")
    public Object GetPlanGanttAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/planGantt");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 设备计划表
     */
    @Log("设备计划表")
    @ApiOperation("设备计划表")
    @PostMapping(value = "/DevicePlans")
    public Object getDevicePlan(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/DevicePlans");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 设备负荷表
     */
    @Log("设备负荷表")
    @ApiOperation("设备负荷表")
    @PostMapping(value = "/DeviceLoads")
    public Object GetDeviceLoadsAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/DeviceLoads");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 删除下发计划
     */
    @Log("删除下发计划")
    @ApiOperation("删除下发计划")
    @PostMapping(value = "/Delete")
    public Object DeleteAsync(@RequestBody Object dto, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/Delete");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 资源冲突
     */
    @Log("资源冲突")
    @ApiOperation("资源冲突")
    @GetMapping(value = "/querySourceConflict/{id}")
    public Object DeleteAsync(@PathVariable String id, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/querySourceConflict/" + id);
        return HttpClientUtil.callMes(request, inputParamDto);
    }


    /**
     * 查看
     */
    @Log("查看")
    @ApiOperation("查看")
    @GetMapping(value = "/View/{id}")
    public Object ViewByIdAsync(@PathVariable String id, HttpServletRequest request) {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/View/" + id);
        return HttpClientUtil.callMes(request, inputParamDto);
    }


    /**
     * 查看
     */
    @Log("下发计划")
    @ApiOperation("下发计划")
    @PutMapping(value = "/issue")
    public Object Issue(@RequestBody Object dto, HttpServletRequest request) throws JsonProcessingException {
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("PUT");
        inputParamDto.setServiceUrl("aps/api/schedulePlan/Issue");
        Object obj = HttpClientUtil.callMes(request, inputParamDto);
        log.info("下发计划aps返回值:"+JSON.toJSONString(obj));
        ObjectMapper mapper = new ObjectMapper();
        String json = (String) obj;
        Map<String, Object> map = mapper.readValue(json, new TypeReference<Map<String, Object>>() {});
        Object data = map.get("data");
        String dataJson = mapper.writeValueAsString(data);
        List<ProductionTaskAPSDTO> productionTaskAPSDTOList = mapper.readValue(dataJson,
                new TypeReference<List<ProductionTaskAPSDTO>>(){});


        productionTaskService.saveBatchAPS(productionTaskAPSDTOList);


        return obj;
    }


}
