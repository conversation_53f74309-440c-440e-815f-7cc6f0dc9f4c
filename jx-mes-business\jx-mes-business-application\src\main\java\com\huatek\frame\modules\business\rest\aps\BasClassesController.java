package com.huatek.frame.modules.business.rest.aps;

import com.gexin.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 班次管理
 */

@Api(tags = "班次管理")
@RestController
@RequestMapping("/api/classes")
public class BasClassesController {
    @Log("获取班次分页信息")
    @ApiOperation(value = "获取班次分页信息")
    @PostMapping(value = "/page", produces = { "application/json;charset=utf-8" })
    public Object GetPagedAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/classes/page");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    @Log("获取班次信息")
    @ApiOperation(value = "获取班次信息")
    @GetMapping(value = "/getall", produces = { "application/json;charset=utf-8" })
    public Object GetPagedAsync(HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/classes/getall");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    @Log("新增班次")
    @ApiOperation(value = "新增班次")
    @TorchPerm("shiftManagement:add")
    @PostMapping(value = "/add", produces = { "application/json;charset=utf-8" })
    public Object CreateAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/classes/add");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    @Log("更新班次")
    @ApiOperation(value = "更新班次")
    @PostMapping(value = "/edit", produces = { "application/json;charset=utf-8" })
    public Object UpdateAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/classes/edit");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    @Log("删除班次")
    @ApiOperation(value = "删除班次")
    @DeleteMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
    public Object UpdateAsync(@RequestParam(value = "id")String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("DELETE");
        inputParamDto.setServiceUrl("aps/api/classes/delete/" + id);
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    @Log("根据ID获取班次")
    @ApiOperation(value = "根据ID获取班次")
    @GetMapping(value = "/getbyid/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetByIdAsync(@PathVariable(value = "id")String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/classes/getbyid/" + id);
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 获取班次信息 工位模块弹框中选择班次
     */
    @Log("获取班次信息 工位模块弹框中选择班次")
    @ApiOperation(value = "获取班次信息 工位模块弹框中选择班次")
    @GetMapping(value = "/getclasses", produces = { "application/json;charset=utf-8" })
    public Object GetByIdAsync( HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/classes/getclasses");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    @Log("批量启用")
    @ApiOperation(value = "批量启用")
    @TorchPerm("shiftManagement:enable")
    @PutMapping(value = "/changestatus/{status}", produces = { "application/json;charset=utf-8" })
    public Object GetByIdAsync(@PathVariable(value = "status")String status, @RequestBody List<String> data, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(data));
        inputParamDto.setHttpMethod("PUT");
        inputParamDto.setServiceUrl("aps/api/classes/changestatus/" + status);
        return HttpClientUtil.callMes(request, inputParamDto);
    }


}
