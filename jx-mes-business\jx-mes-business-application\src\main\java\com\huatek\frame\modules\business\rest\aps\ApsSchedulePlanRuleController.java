package com.huatek.frame.modules.business.rest.aps;

import com.gexin.fastjson.JSON;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.modules.business.service.dto.InputParamDto;
import com.huatek.frame.modules.business.utils.HttpClientUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 排产规则管理
 */
@Api(tags = "排产规则管理")
@RestController
@RequestMapping("/api/schedulePlanRules")
public class ApsSchedulePlanRuleController {
    /**
     * 获取排产规则分页信息
     */
    @Log("获取排产规则分页信息")
    @ApiOperation(value = "获取排产规则分页信息")
    @PostMapping(value = "/page", produces = { "application/json;charset=utf-8" })
    public Object GetPagedAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlanRules/page");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 新增排产规则
     */
    @Log("新增排产规则")
    @ApiOperation(value = "新增排产规则")
    @PostMapping(value = "/add", produces = { "application/json;charset=utf-8" })
    public Object CreateAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlanRules/add");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 更新排产规则
     */
    @Log("更新排产规则")
    @ApiOperation(value = "更新排产规则")
    @PostMapping(value = "/edit", produces = { "application/json;charset=utf-8" })
    public Object UpdateAsync(@RequestBody Object dto, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam(JSON.toJSONString(dto));
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlanRules/edit");
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 删除排产规则
     */
    @Log("删除排产规则")
    @ApiOperation(value = "删除排产规则")
    @PostMapping(value = "/delete/{id}", produces = { "application/json;charset=utf-8" })
    public Object DeleteAsync(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("POST");
        inputParamDto.setServiceUrl("aps/api/SchedulePlanRules/delete/" + id);
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 根据id获取排产规则
     */
    @Log("根据id获取排产规则")
    @ApiOperation(value = "根据id获取排产规则")
    @PostMapping(value = "/{id}", produces = { "application/json;charset=utf-8" })
    public Object GetByIdAsync(@PathVariable String id, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/SchedulePlanRules/" + id);
        return HttpClientUtil.callMes(request, inputParamDto);
    }

    /**
     * 根据工单编号获取排产规则
     */
    @Log("根据工单编号获取排产规则")
    @ApiOperation(value = "根据工单编号获取排产规则")
    @PostMapping(value = "/{scheduleCode}", produces = { "application/json;charset=utf-8" })
    public Object GetByScheduleCodeAsync(@PathVariable String scheduleCode, HttpServletRequest request){
        InputParamDto inputParamDto = new InputParamDto();
        inputParamDto.setParam("");
        inputParamDto.setHttpMethod("GET");
        inputParamDto.setServiceUrl("aps/api/SchedulePlanRules/" + scheduleCode);
        return HttpClientUtil.callMes(request, inputParamDto);
    }
}
