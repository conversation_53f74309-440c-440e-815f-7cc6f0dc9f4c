package com.huatek.tool.modules.poi;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.util.StringUtil;
import org.apache.poi.util.Units;
import org.apache.poi.wp.usermodel.HeaderFooterType;
import org.apache.poi.xwpf.model.XWPFHeaderFooterPolicy;
import org.apache.poi.xwpf.usermodel.IBodyElement;
import org.apache.poi.xwpf.usermodel.ParagraphAlignment;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFHeader;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFPicture;
import org.apache.poi.xwpf.usermodel.XWPFPictureData;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.apache.xmlbeans.XmlCursor;
import org.apache.xmlbeans.XmlException;
import org.apache.xmlbeans.XmlObject;
import org.openxmlformats.schemas.drawingml.x2006.main.CTGraphicalObject;
import org.openxmlformats.schemas.drawingml.x2006.wordprocessingDrawing.CTAnchor;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBody;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTDrawing;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageMar;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPageSz;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTblBorders;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcMar;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTTcPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTVerticalJc;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STBrType;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STFldCharType;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STVerticalJc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

/**
 * word格式报表导出工具
 * @name ReportDocxTool
 * @description
 * @version V
 * <AUTHOR>
 * @time 2025年8月8日 下午2:00:33
 */
public class ReportDocxTool {

	Logger logger = LoggerFactory.getLogger(ReportDocxTool.class);

	private Pattern pattern = Pattern.compile("\\$\\{([a-zA-Z0-9\\(\\)\\.\\s\\?:]+)\\}");
	private Pattern kp = Pattern.compile("([a-zA-Z0-9\\s]+):?([a-zA-Z0-9\\s]+)?\\??([a-zA-Z0-9\\s]+)?");
	private Map<String, String> keyWord = new HashMap<String, String>();
	{
		keyWord.put("pageCount", "NUMPAGES  \\\\* Arabic  \\\\* MERGEFORMAT");
		keyWord.put("pageIndex", "PAGE \\\\* ARABIC MERGEFORMAT");
	}

	public static void main(String[] args) {
		ReportDocxTool rt = new ReportDocxTool();
		JSONObject jsonData = TempData.createTempData();
//		System.out.println(jsonData.toJSONString());

		rt.writeDOCX("D:/资料/君信/报表工具/table_temp_1.docx", jsonData, "D:/资料/君信/报表工具/table_" + System.currentTimeMillis() + ".docx");
	}


	public XWPFDocument createDOCXDocument(String template, JSONObject jsonData) {
		return createDOCXDocument(new File(template), jsonData);
	}

    public XWPFDocument createDOCXDocument(File template, JSONObject jsonData) {
		try {
			List<XWPFParagraph> loopSigns = new ArrayList<XWPFParagraph>();
			InputStream is = new FileInputStream(template);
			XWPFDocument temp = new XWPFDocument(is);
			XWPFDocument doc = new XWPFDocument();
			copyStyle(temp, doc);
			fillHeader(temp, doc, jsonData);

			temp.getBodyElementsIterator().forEachRemaining(item -> {
				if("TABLE".equals(item.getElementType().name())) {
					XWPFTable t = (XWPFTable)item;
					if(!CollectionUtils.isEmpty(loopSigns)) {
						//动态循环输出表格
						XWPFParagraph xwpfParagraph = loopSigns.get(loopSigns.size() - 1);
						String[] attr = readAttrName(xwpfParagraph);
						JSONArray tabs = jsonData.getJSONArray(attr[0]);
						for(int i = 0; i < tabs.size(); i++) {
							JSONObject tabData = tabs.getJSONObject(i);
							XWPFParagraph paragraph = doc.createParagraph();
							XmlCursor cursor= paragraph.getCTP().newCursor();//循环起点游标位置,上下文发生变化时游标必须重新获取
							XWPFTable table = doc.insertNewTbl(cursor);
							copyTable(table, t);
							fillTableData(doc, table, tabData);
						}
					} else {
						//简单的表格内容填充
						XWPFParagraph paragraph = doc.createParagraph();
						XmlCursor cursor= paragraph.getCTP().newCursor();//循环起点游标位置,上下文发生变化时游标必须重新获取
						XWPFTable table = doc.insertNewTbl(cursor);
						copyTable(table, t);
						fillTableData(doc, table, jsonData);
					}
				} else if("PARAGRAPH".equals(item.getElementType().name())) {
					XWPFParagraph p = (XWPFParagraph)item;
					Matcher matcher = pattern.matcher(p.getText());
					if(matcher.find()) {
						String key = matcher.group(1);
						Matcher km = kp.matcher(key);
						if(km.find()) {
							if("forEach".equals(km.group(1))) {
								loopSigns.add(p);
							} else if("endEach".equals(km.group(1))) {
								loopSigns.remove(p);
							} else {
								createParagraph(doc, p, jsonData, km.group(1), km.group(3));
							}
						}
					} else {
						XWPFParagraph paragraph = doc.createParagraph();
						copyParagraph(paragraph, p);
					}
				}
			});
			temp.close();
			return doc;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public void writeDOCX(String template, JSONObject jsonData, String outputPath) {
		writeDOCX(new File(template), jsonData, outputPath);
	}

    public void writeDOCX(File template, JSONObject jsonData, String outputPath) {
    	writeDOCX(createDOCXDocument(template, jsonData), outputPath);
	}

    /**
     * 文件输出
     * @param doc
     * @param fos
     */
    public static void writeDOCX(XWPFDocument doc, FileOutputStream fos) {
    	try {
    		doc.write(fos);
    		fos.flush();
    		fos.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
    }

    /**
     * 文件输出
     * @param doc
     * @param outputPath
     */
    private void writeDOCX(XWPFDocument doc, String outputPath) {
    	try {
    		FileOutputStream fos = new FileOutputStream(outputPath);
    		doc.write(fos);
    		fos.flush();
    		fos.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
    }

    /**
     * 生成段落
     * @param doc 报表
     * @param p   模板中段落
     * @param jsonData    源数据
     * @param mainAttr    源数据主属性
     * @param backupAttr  源数据备选字段
     */
    private void createParagraph(XWPFDocument doc, XWPFParagraph p, JSONObject jsonData, String mainAttr, String backupAttr) {
    	if(jsonData.containsKey(mainAttr)) {
    		if("JSONObject".equals(jsonData.get(mainAttr).getClass().getSimpleName())) {
    			JSONObject attrData = jsonData.getJSONObject(mainAttr);
    			replaceObject(doc, attrData);
    		} else {
    			XWPFParagraph paragraph = doc.createParagraph();
        		copyParagraph(paragraph, p);
        		replaceWildcard(paragraph, jsonData);
    		}
    	} else {
    		XWPFParagraph paragraph = doc.createParagraph();
    		copyParagraph(paragraph, p);
    		replaceWildcard(paragraph, jsonData);
    	}
	}

    /**
     * 复制模板样式到生成的文档
     * @param source
     * @param target
     */
    private void copyStyle(XWPFDocument source, XWPFDocument target) {
    	CTBody sbody = source.getDocument().getBody();
    	if(!sbody.isSetSectPr()) {
    		return;
    	}
    	CTSectPr sectPr = sbody.getSectPr();
    	CTBody tbody = target.getDocument().getBody();
    	CTSectPr ps = tbody.isSetSectPr() ? tbody.getSectPr() : tbody.addNewSectPr();

    	ps.setDocGrid(sectPr.getDocGrid());

    	CTPageSz pageSize = ps.addNewPgSz();
    	pageSize.setW(sectPr.getPgSz().getW());
    	pageSize.setH(sectPr.getPgSz().getH());
    	pageSize.setOrient(sectPr.getPgSz().getOrient());

    	CTPageMar pm = ps.addNewPgMar();
    	pm.setTop(sectPr.getPgMar().getTop());
    	pm.setBottom(sectPr.getPgMar().getBottom());
    	pm.setLeft(sectPr.getPgMar().getLeft());
    	pm.setRight(sectPr.getPgMar().getRight());

    }

    /**
     * 表格数据填充
     * @param table
     * @param tabData
     */
    private void fillTableData(XWPFDocument doc, XWPFTable table, JSONObject tabData) {
    	int maxCols = getMaxColumns(table);
    	//处理表格中的循环占位符
    	boolean b = true;
    	while(b) {
    		int insertPoint = -1;
    		String[] loopKeys = null;
        	for(int i = 0; i < table.getRows().size(); i++) {
        		XWPFTableRow row = table.getRows().get(i);
        		if(row.getTableCells().size() == 1) {
        			loopKeys = analyzeLoop(row.getTableCells().get(0));
        			if(loopKeys != null) {
        				insertPoint = i;
        				break;
        			}
        		}
        	}

        	if(insertPoint >= 0) {
        		table.removeRow(insertPoint);//删除循环占位符所在行
        		if(tabData.containsKey(loopKeys[0])) {
        			JSONObject loopData = tabData.getJSONObject(loopKeys[0]);
        			insertNewRow(doc, insertPoint, table, loopData, maxCols);
        		} else if (loopKeys[1] != null && tabData.containsKey(loopKeys[1])){
        			XWPFTableRow row = table.insertNewTableRow(insertPoint);
        			XWPFTableCell cell = row.getCell(0) == null ? row.addNewTableCell() : row.getCell(0);
        			CTTcPr pr = cell.getCTTc().addNewTcPr();
        			pr.addNewGridSpan().setVal(new BigInteger(maxCols + ""));
        			XWPFParagraph tp = cell.getParagraphArray(0) == null ? cell.addParagraph() : cell.getParagraphArray(0);
        			XWPFRun run = CollectionUtils.isEmpty(tp.getRuns()) ? tp.createRun() : tp.getRuns().get(0);
        			if("JSONObject".equals(tabData.get(loopKeys[1]).getClass().getSimpleName())) {
        				JSONObject df = tabData.getJSONObject(loopKeys[1]);
        				JSONObject config = df.getJSONObject("config");
        				if(config != null) {
        					if(config.containsKey("height")) {
//        						CTTrPr trPr = row.getCtRow().isSetTrPr() ? row.getCtRow().getTrPr() : row.getCtRow().addNewTrPr();
//        						CTHeight ht = trPr.addNewTrHeight();
//        						ht.setVal(config.getIntValue("height") * 20);
        						row.setHeight(config.getIntValue("height")*20);//单位是磅
        					}
        					if(config.containsKey("margin")) {
        						JSONArray margin = config.getJSONArray("margin");
        						CTTcMar mar = pr.addNewTcMar();
        						mar.addNewTop().setW((int)margin.get(0)*20);
        						mar.addNewRight().setW((int)margin.get(1)*20);
        						mar.addNewBottom().setW((int)margin.get(2)*20);
        						mar.addNewLeft().setW((int)margin.get(3)*20);
        					}
        				}
        				run.setText(df.getString("content"));
        			} else {
        				Object value = tabData.get(loopKeys[1]);
        				run.setText(value == null ? "" : value.toString());
        			}
        		}
        		//变量初始化
        		insertPoint = -1;
        		loopKeys = null;
        	} else {
        		b = false;
        	}
    	}

    	//数据替换
    	for(int i = 0; i < table.getRows().size(); i++) {
    		XWPFTableRow row = table.getRows().get(i);
			for(XWPFTableCell cell : row.getTableCells()) {
				List<XWPFParagraph> ps = cell.getParagraphs();
				for(XWPFParagraph p : ps) {
		        	if(p.getRuns().isEmpty()) {
			        	continue;
			        }
		        	replaceWildcard(p, tabData);
				}
			}
		}
    }


    /**
     * 在表格指定索引处插入新的数据行
     * @param insertPoint
     * @param table
     * @param loopData
     * @param maxCols
     */
    private void insertNewRow(XWPFDocument doc, int insertPoint, XWPFTable table, JSONObject loopData, int maxCols) {
    	//自定义每列的跨度， 总和必须等于最大列数maxCols，默认每列跨度为1
    	int[] cellSpans = null;//

    	if(loopData.containsKey("dataType") && "table".equals(loopData.getString("dataType"))) {
    		XWPFTableRow row = table.insertNewTableRow(insertPoint);
    		XWPFTableCell cell = row.getCell(0) == null ? row.addNewTableCell() : row.getCell(0);
    		CTTcPr pr = cell.getCTTc().addNewTcPr();
    		pr.addNewGridSpan().setVal(new BigInteger(maxCols + ""));
    		createCell(doc, cell, loopData);
    		return;
    	}

    	//自定义行的高度，每条数据可能需要多行展示
    	Integer[] rowHeights = null;
    	//自定义单元格的边距[上、右、下、左]
    	JSONArray margin = null;

    	//单元格的配置参数
		JSONObject conf = loopData.getJSONObject("config");
		if(conf != null) {
			if(conf.containsKey("cellSpans")) {
				cellSpans = (int[])conf.get("cellSpans");
			}
			if(conf.containsKey("rowHeights")) {
				rowHeights = (Integer[])conf.get("rowHeights");
			}

			if(conf.containsKey("margin")) {
				margin = conf.getJSONArray("margin");
			}
		}

		JSONArray datas = loopData.getJSONArray("datas");
		List<XWPFTableRow> rows = new ArrayList<XWPFTableRow>();
		int rowsBatch = datas.getJSONObject(0).getJSONArray("rows").size();
		for(int i = 0; i < datas.size(); i++) {
			JSONArray dr = datas.getJSONObject(i).getJSONArray("rows");//每条数据中的多行数据
			if(cellSpans == null) {
				cellSpans = new int[maxCols];
			}
			if(i%cellSpans.length == 0) {
				rows.clear();
				for(int r = 0; r < rowsBatch; r++) {
					XWPFTableRow row = table.insertNewTableRow(insertPoint);
					if(rowHeights != null && rowHeights[r] != null) {
						row.setHeight(rowHeights[r]*20);
					}
					for(int c = 0; c < cellSpans.length; c++) {
						XWPFTableCell cell = row.getCell(c) == null ? row.addNewTableCell() : row.getCell(c);
						CTTcPr pr = cell.getCTTc().addNewTcPr();
						CTVerticalJc va = pr.addNewVAlign();
		                va.setVal(STVerticalJc.CENTER);//设置垂直居中
						if(cellSpans[c] != 0) {
		        			pr.addNewGridSpan().setVal(new BigInteger(cellSpans[c] + ""));
						}
						if(margin != null) {
							CTTcMar mar = pr.addNewTcMar();
							mar.addNewTop().setW((int)margin.get(0)*20);
							mar.addNewRight().setW((int)margin.get(1)*20);
							mar.addNewBottom().setW((int)margin.get(2)*20);
							mar.addNewLeft().setW((int)margin.get(3)*20);
						}
					}
					rows.add(row);
					insertPoint++;
				}
			}

			for(int r = 0; r < rowsBatch; r++) {
				XWPFTableCell cell = rows.get(r).getCell(i%cellSpans.length);
				JSONObject cellData = dr.getJSONObject(r);//单元格的数据对象
			    createCell(doc, cell, cellData);
			}
		}
	}

    /**
     * 根据单元格数据类型填充数据
     * @param cell
     * @param cellData
     */
    private void createCell(XWPFDocument doc, XWPFTableCell cell, JSONObject cellData) {
    	String dataType = cellData.getString("dataType");
    	XWPFParagraph tp = cell.getParagraphArray(0) == null ? cell.addParagraph() : cell.getParagraphArray(0);
    	tp.setAlignment(ParagraphAlignment.CENTER);//设置水平居中
		XWPFRun run = CollectionUtils.isEmpty(tp.getRuns()) ? tp.createRun() : tp.getRuns().get(0);
    	if("string".equals(dataType)) {
			run.setText(cellData.getString("content"));
    	} else if("image".equals(dataType)) {
    		if(cellData.containsKey("content")) {
    			try {
        			int w = cellData.containsKey("w") ? cellData.getIntValue("w") : 200;
        			int h = cellData.containsKey("h") ? cellData.getIntValue("h") : 200;
    				run.addPicture(getStream(cellData.getBytes("content")), XWPFDocument.PICTURE_TYPE_JPEG, cellData.getString("name"), Units.toEMU(w), Units.toEMU(h));
    			} catch (Exception e) {
    				e.printStackTrace();
    			}
    		} else {
    			run.setText("");
    		}
    	} else if("table".equals(dataType)) {
			//嵌套表
//			CTTbl cttbl = cell.getCTTc().addNewTbl();
//	    	CTRow row = cttbl.addNewTr();
//	    	CTTc tc = row.addNewTc();
    	}
    }

    private InputStream getStream(byte[] data) {
    	ByteArrayInputStream inputStream = new ByteArrayInputStream(data);
    	return inputStream;
    }
	/**
     * 获取表格的最大列数
     * @param table
     * @return
     */
    private int getMaxColumns(XWPFTable table) {
        int maxColumns = 0;
        for (XWPFTableRow row : table.getRows()) {
            maxColumns = Math.max(maxColumns, row.getTableCells().size());
        }
        return maxColumns;
    }

    /**
     * 提取单元格内容，如果内容包含循环参数则返回参数
     * @param cell
     * @return
     */
    private String[] analyzeLoop(XWPFTableCell cell) {
		Matcher matcher = pattern.matcher(cell.getText());
		while(matcher.find()) {
			String key = matcher.group(1);
			Matcher km = kp.matcher(key);
			while(km.find()) {
				if("foreach".equals(km.group(1).trim().toLowerCase())) {
					return new String[]{km.group(2).trim(), km.group(3) != null ? km.group(3).trim() : null};
				}
			}
		}
    	return null;
    }

    /**
     * 替换段落中的通配符（对象数据）
     * @param doc
     * @param attrData
     */
    private void replaceObject(XWPFDocument doc, JSONObject attrData) {
    	if(attrData.containsKey("dataType") && attrData.get("dataType") != null) {
    		String dataType = attrData.getString("dataType");
        	if("table".equals(dataType)) {
        		createTable(doc, attrData);
        	}
    	} else {
    		logger.error("数据格式错误：缺少数据类型字段dataType,源数据{}", attrData.toJSONString());
    	}
    }

    /**
     * 根据数据对象在word文件中生成对应的表格
     * @param doc
     * @param attrData
     */
    private void createTable(XWPFDocument doc, JSONObject attrData) {
    	XWPFParagraph paragraph = doc.createParagraph();
    	createTable(doc, paragraph, attrData);
    }

    private void createTable(XWPFDocument doc, XWPFParagraph paragraph, JSONObject attrData) {
    	XmlCursor cursor= paragraph.getCTP().newCursor();//游标位置
		XWPFTable table = doc.insertNewTbl(cursor);
        table.setWidth(9000);
		JSONArray thead = attrData.getJSONArray("thead");

		JSONArray tbody = attrData.getJSONArray("tbody");
		XWPFTableRow row = table.getRow(0) != null ? table.getRow(0) : table.createRow();
		for(int i = 0; i < thead.size(); i++) {
			JSONObject th = thead.getJSONObject(i);
			XWPFTableCell cell = row.getCell(i) == null ? row.addNewTableCell() : row.getCell(i);
//			CTTcPr pr = cell.getCTTc().addNewTcPr();
//			pr.addNewGridSpan().setVal(new BigInteger("2"));
			XWPFParagraph tp = cell.getParagraphArray(0) == null ? cell.addParagraph() : cell.getParagraphArray(0);
			XWPFRun run = CollectionUtils.isEmpty(tp.getRuns()) ? tp.createRun() : tp.getRuns().get(0);
			run.setText(th.getString("name"));
		}

		for(int i = 0; i < tbody.size(); i++) {
			JSONObject data = tbody.getJSONObject(i);
			XWPFTableRow r = table.createRow();
			for(int c = 0; c < thead.size(); c++) {
				JSONObject th = thead.getJSONObject(c);
				XWPFTableCell cell = r.getCell(c) == null ? r.addNewTableCell() : r.getCell(c);
				XWPFParagraph tp = cell.getParagraphArray(0) == null ? cell.addParagraph() : cell.getParagraphArray(0);
				XWPFRun run = CollectionUtils.isEmpty(tp.getRuns()) ? tp.createRun() : tp.getRuns().get(0);
				Object value = data.get(th.getString("code"));
				run.setText(value == null ? "" : value.toString());
			}
		}
    }

    /**
     * 替换段落中的通配符（普通文本）
     * @param p
     * @param data
     */
    private void replaceWildcard(XWPFParagraph p, JSONObject data) {
    	String cc = "";//完整的文本，有些通配符会被分成好几个run需要进行合并
    	boolean clear = false;
    	for(XWPFRun r : p.getRuns()) {
    		String c = r.text();
//System.out.println(c);
    		if(StringUtil.isBlank(c)) {
    			continue;
    		}
    		if(r.getCTR().getFldCharArray() != null && r.getCTR().getFldCharArray().length > 0 && r.getCTR().getFldCharArray(0).getFldCharType() == STFldCharType.BEGIN) {
    			clear = true;
    			continue;
    		}

    		if(r.getCTR().getFldCharArray() != null && r.getCTR().getFldCharArray().length > 0 && r.getCTR().getFldCharArray(0).getFldCharType() == STFldCharType.END) {
    			clear = false;
    			continue;
    		}
    		if(clear) {
    			r.setText("", 0);
    			continue;
    		}
    		Matcher matcher = pattern.matcher(c);
    		while(matcher.find()) {
    			String key = matcher.group(1);
    			Matcher km = kp.matcher(key);
    			String value = "";
				if(km.find()) {
					if(data.containsKey(km.group(1))) {
						value = data.getString(km.group(1));
					} else {
						value = data.getString(km.group(3));
					}
				}
    			c = c.replaceAll("\\$\\{" + key + "\\}", value);
    			matcher = pattern.matcher(c);
    		}
    		r.setText(c, 0);

    		if(c.indexOf("$") >= 0) {
    			cc = r.text();
    			r.setText("", 0);
    			continue;
    		}
    		if(c.indexOf("}") >= 0) {
    			cc += r.text();
    			Matcher sm = pattern.matcher(cc);
        		while(sm.find()) {
        			String key = sm.group(1);
        			Matcher km = kp.matcher(key);
        			String value = "";
    				if(km.find()) {
    					if(data.containsKey(km.group(1))) {
    						value = data.getString(km.group(1));
    					} else {
    						value = data.getString(km.group(3));
    					}
    				}
        			cc = cc.replaceAll("\\$\\{" + key + "\\}", value);
        			sm = pattern.matcher(cc);
        		}
    			r.setText(cc, 0);
    			cc = "";
    			continue;
    		}
    		if(!StringUtil.isBlank(cc)) {
    			cc += c;
    			r.setText("", 0);
    		}
    	}
    }

    /**
     * 从循环标识中读取主备属性名
     * @param p
     * @return
     */
    private String[] readAttrName(XWPFParagraph p) {
    	String[] r = new String[2];
    	Matcher matcher = pattern.matcher(p.getText());
		if(matcher.find()) {
			String key = matcher.group(1);
			Matcher km = kp.matcher(key);
			if(km.find()) {
				if("forEach".equals(km.group(1))) {
					r[0] = km.group(2) == null ? null : km.group(2).trim();
					r[1] = km.group(3) == null ? null : km.group(3).trim();
				}
			}
		}
    	return r;
    }

    /**
     * 复制表格及样式
     * @param targetTable
     * @param sourceTable
     */
    private void copyTable(XWPFTable targetTable, XWPFTable sourceTable) {
//    	XmlObject xml = sourceTable.getCTTbl().copy();
//    	System.out.println(xml.toString());
//    	System.out.println("===================================================");
    	//设置表格左对齐缩进
    	targetTable.getCTTbl().getTblPr().setTblInd(sourceTable.getCTTbl().getTblPr().getTblInd());
    	CTTblBorders sbs = sourceTable.getCTTbl().getTblPr().getTblBorders();
    	if(sbs != null) {
    		CTTblBorders tbs = targetTable.getCTTbl().getTblPr().addNewTblBorders();
    		if(sbs.getTop() != null) {
    			tbs.setTop(sbs.getTop());
    		}

    		if(sbs.getBottom() != null) {
    			tbs.setBottom(sbs.getBottom());
    		}

    		if(sbs.getLeft() != null) {
    			tbs.setLeft(sbs.getLeft());
    		}

    		if(sbs.getRight() != null) {
    			tbs.setRight(sbs.getRight());
    		}

    		if(sbs.getInsideH() != null) {
    			tbs.setInsideH(sbs.getInsideH());
    		}

    		if(sbs.getInsideV() != null) {
    			tbs.setInsideV(sbs.getInsideV());
    		}

    	}
    	for(int r = 0; r < sourceTable.getRows().size(); r++) {
    		if(targetTable.getRow(r) != null) {
    			copyRow(targetTable.getRow(r), sourceTable.getRow(r));
    		} else {
    			copyRow(targetTable.createRow(), sourceTable.getRow(r));
    		}
    	}
    }

    /**
     * 嵌套表
     * @param cell
     */
    private void nestTable(CTTc tc, String val) {
    	tc.addNewP().addNewR().addNewT().setStringValue(val);
    }

	/**
	 * 复制表格行内容及单元格样式
	 * @param targetRow
	 * @param sourceRow
	 */
	private void copyRow(XWPFTableRow targetRow, XWPFTableRow sourceRow) {
//		sourceRow.getCtRow().
		targetRow.getCtRow().setTrPr(sourceRow.getCtRow().getTrPr());
	    List<XWPFTableCell> tableCells = sourceRow.getTableCells();
	    if (CollectionUtils.isEmpty(tableCells)) {
	        return;
	    }
	    targetRow.setHeight(sourceRow.getHeight());
	    int i = 0;
	    for (XWPFTableCell sourceCell : tableCells) {
	        XWPFTableCell newCell = targetRow.getCell(i) == null ? targetRow.addNewTableCell() : targetRow.getCell(i);
	        CTTcPr scttcpr = sourceCell.getCTTc().getTcPr();
	        newCell.getCTTc().setTcPr(scttcpr);
	        List<XWPFParagraph> sourceParagraphs = sourceCell.getParagraphs();
	        if (CollectionUtils.isEmpty(sourceParagraphs)) {
	            continue;
	        }
//System.out.println(sourceCell.getText() + "----" + sourceRow.getHeight());
	        int p = 0;
	        for(XWPFParagraph sp : sourceParagraphs) {
	        	XWPFParagraph tp = newCell.getParagraphArray(p) == null ? newCell.addParagraph() : newCell.getParagraphArray(p);
        		tp.getCTP().setPPr(sp.getCTP().getPPr());
	        	tp.setAlignment(sp.getAlignment());
	        	tp.setVerticalAlignment(sp.getVerticalAlignment());
	        	tp.setSpacingAfter(sp.getSpacingAfter());//设置行间距
	        	tp.setSpacingBefore(sp.getSpacingBefore());//设置行间距
	        	int r = 0;
	        	for(XWPFRun sr : sp.getRuns()) {
	        		XWPFRun run = CollectionUtils.isEmpty(tp.getRuns()) || tp.getRuns().size() <= r ? tp.createRun() : tp.getRuns().get(r);
	        		copyRun(run, sr);
	        		r++;
	        	}
	        	p++;
	        }
	        i++;
	    }
	}

	/**
	 * 填充页眉页脚
	 * @param temp        模板
	 * @param doc         生成的报表
	 * @param jsonData    报表数据
	 */
	private void fillHeader(XWPFDocument temp, XWPFDocument doc, JSONObject jsonData) {
		XWPFHeaderFooterPolicy policy = temp.getHeaderFooterPolicy();
	    XWPFHeader firstPageHeader = policy.getFirstPageHeader();
	    if (firstPageHeader != null) {
	        createHeader(firstPageHeader, doc, HeaderFooterType.FIRST, jsonData);
	    }
	    XWPFHeader defaultHeader = policy.getDefaultHeader();
	    if (defaultHeader != null) {
	        createHeader(defaultHeader, doc, HeaderFooterType.DEFAULT, jsonData);
	    }
	}

	private void createHeader(XWPFHeader pageHeader, XWPFDocument doc, HeaderFooterType type, JSONObject jsonData) {
		XWPFHeader header = doc.createHeader(type);
		for(int i = 0; i < pageHeader.getBodyElements().size(); i++) {
			IBodyElement item = pageHeader.getBodyElements().get(i);
			if("TABLE".equals(item.getElementType().name())) {
				XWPFTable t = (XWPFTable)item;
				XWPFTable table = header.createTable(t.getNumberOfRows(), t.getRows().get(0).getTableCells().size());
				copyTable(table, t);
				fillTableData(doc, table, jsonData);
			} else if("PARAGRAPH".equals(item.getElementType().name())) {
				XWPFParagraph p = (XWPFParagraph)item;
				XWPFParagraph paragraph = header.createParagraph();
				copyParagraph(paragraph, p);
				replaceWildcard(paragraph, jsonData);
			}
		}
	}

	/**
	 * 复制段落及样式
	 * @param target
	 * @param source
	 */
	private void copyParagraph(XWPFParagraph target, XWPFParagraph source) {
//		System.out.println(source.getText());
//		target.setAlignment(source.getAlignment());
		if(source.getRuns().isEmpty()) {
			target.getCTP().set(source.getCTP());
		} else {
			// 设置段落样式
			target.getCTP().setPPr(source.getCTP().getPPr());
			// 移除所有的run
			for (int pos = target.getRuns().size() - 1; pos >= 0; pos--) {
				target.removeRun(pos);
			}
			// 复制新的run
			for (XWPFRun s : source.getRuns()) {
				XWPFRun targetrun = target.createRun();
				copyRun(targetrun, s);
			}
		}
		target.setSpacingAfter(source.getSpacingAfter());//设置行间距
		target.setSpacingBefore(source.getSpacingBefore());//设置行间距
	}

	/**
	 * 复制run
	 * @param target
	 * @param source
	 */
	private void copyRun(XWPFRun target, XWPFRun source) {
//		XmlObject xml2 = source.getCTR().copy();
//		target.getCTR().set(xml);

		if(source.text().isEmpty()) {
			List<XWPFPicture> ps = source.getEmbeddedPictures();
			if(ps != null && ps.size() > 0) {
				target.getCTR().setRPr(source.getCTR().getRPr());
				try {
					CTAnchor chor = null;
					if(!source.getCTR().getDrawingArray(0).getAnchorList().isEmpty()) {
						chor = source.getCTR().getDrawingArray(0).getAnchorList().get(0);
					}

					XWPFPictureData pd = ps.get(0).getPictureData();
					target.addPicture(getStream(pd.getData()), pd.getPictureType(), pd.getFileName(), Units.toEMU(ps.get(0).getWidth()), Units.toEMU(ps.get(0).getDepth()));
			        if(chor != null) {//浮动图片要添加浮动属性
			        	CTDrawing drawing = target.getCTR().getDrawingArray(0);
				        CTGraphicalObject graphicalobject = drawing.getInlineArray(0).getGraphic();

				        //拿到新插入的图片替换添加CTAnchor 设置浮动属性 删除inline属性
				        CTAnchor anchor = getAnchorWithGraphic(graphicalobject, pd.getFileName(),
				        		Units.toEMU(ps.get(0).getWidth()), Units.toEMU(ps.get(0).getDepth()),//图片大小
				        		chor.getPositionH().getPosOffset(), chor.getPositionV().getPosOffset(), false);//相对当前段落位置 需要计算段落已有内容的左偏移
				        drawing.setAnchorArray(new CTAnchor[]{anchor});//添加浮动属性
				        drawing.removeInline(0);
			        }
				} catch (Exception e) {
					e.printStackTrace();
				}
			} else {
				XmlObject xml = source.getCTR().copy();
				target.getCTR().set(xml);
			}
		} else {
			if(source.getCTR().getBrList() != null && source.getCTR().getBrList().size() >= 1) {
				if(source.getCTR().getBrList().get(0).getType() == STBrType.PAGE) {
					target.getCTR().addNewBr().setType(STBrType.PAGE);
					return;
				}
			}
			// 设置run属性
			target.getCTR().setRPr(source.getCTR().getRPr());
			// 设置文本
			target.setText(source.text());
		}
	}

    /**
     * @param ctGraphicalObject 图片数据
     * @param deskFileName      图片描述
     * @param width             宽
     * @param height            高
     * @param leftOffset        水平偏移 left
     * @param topOffset         垂直偏移 top
     * @param behind            文字上方，文字下方
     * @return
     * @throws Exception
     */
    private CTAnchor getAnchorWithGraphic(CTGraphicalObject ctGraphicalObject,
                                                String deskFileName, int width, int height,
                                                int leftOffset, int topOffset, boolean behind) {
        String anchorXML =
                "<wp:anchor xmlns:wp=\"http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing\" "
                        + "simplePos=\"0\" relativeHeight=\"0\" behindDoc=\"" + ((behind) ? 1 : 0) + "\" locked=\"0\" layoutInCell=\"1\" allowOverlap=\"1\">"
                        + "<wp:simplePos x=\"0\" y=\"0\"/>"
                        + "<wp:positionH relativeFrom=\"column\">"
                        + "<wp:posOffset>" + leftOffset + "</wp:posOffset>"
                        + "</wp:positionH>"
                        + "<wp:positionV relativeFrom=\"paragraph\">"
                        + "<wp:posOffset>" + topOffset + "</wp:posOffset>" +
                        "</wp:positionV>"
                        + "<wp:extent cx=\"" + width + "\" cy=\"" + height + "\"/>"
                        + "<wp:effectExtent l=\"0\" t=\"0\" r=\"0\" b=\"0\"/>"
                        + "<wp:wrapNone/>"
                        + "<wp:docPr id=\"1\" name=\"Drawing 0\" descr=\"" + deskFileName + "\"/><wp:cNvGraphicFramePr/>"
                        + "</wp:anchor>";

        CTDrawing drawing = null;
        try {
            drawing = CTDrawing.Factory.parse(anchorXML);
        } catch (XmlException e) {
            e.printStackTrace();
        }
        CTAnchor anchor = drawing.getAnchorArray(0);
        anchor.setGraphic(ctGraphicalObject);
        return anchor;
    }

}
